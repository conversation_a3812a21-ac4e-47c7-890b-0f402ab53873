# Deployment Guide

## Vercel Deployment (Frontend)

### Prerequisites
- GitHub account with your portfolio repository
- Vercel account (free tier available)

### Step 1: Prepare for Deployment

1. **Ensure all images are in the correct location:**
   ```
   client/public/images/
   ├── profile/
   │   └── anshika-profile.jpg
   └── portfolio/
       ├── manipulation/
       ├── matte/
       ├── photo-editing/
       └── poster-design/
   ```

2. **Verify environment variables:**
   - Check `client/.env` has the production API URL
   - Ensure `client/.env.example` is committed to repository

### Step 2: Deploy to Vercel

1. **Connect Repository:**
   - Go to [vercel.com](https://vercel.com)
   - Sign in with GitHub
   - Click "New Project"
   - Import your `Anshika_portfolio` repository

2. **Configure Build Settings:**
   - **Framework Preset:** Vite
   - **Root Directory:** `client`
   - **Build Command:** `npm run build`
   - **Output Directory:** `dist`

3. **Set Environment Variables:**
   - In Vercel dashboard, go to Project Settings → Environment Variables
   - Add: `VITE_API_BASE_URL` = `https://anshika-portfolio-d16e.onrender.com`

4. **Deploy:**
   - Click "Deploy"
   - Wait for build to complete
   - Your site will be available at `https://your-project-name.vercel.app`

### Step 3: Custom Domain (Optional)

1. **Add Custom Domain:**
   - Go to Project Settings → Domains
   - Add your custom domain
   - Follow DNS configuration instructions

## Backend Deployment (Render)

### Current Setup
- Backend is already deployed on Render
- URL: `https://anshika-portfolio-d16e.onrender.com`
- MongoDB Atlas database connected

### Environment Variables on Render
```
MONGODB_URI=your_mongodb_atlas_connection_string
PORT=5000
```

## Troubleshooting

### Images Not Loading
✅ **Fixed:** Images moved to `/public/images/` directory
✅ **Fixed:** All image paths updated to use `/images/` instead of `/src/assets/`

### Common Issues

1. **Build Fails:**
   - Check that `client` is set as root directory
   - Verify all dependencies are in `package.json`
   - Check for any TypeScript errors

2. **Environment Variables:**
   - Ensure `VITE_API_BASE_URL` is set in Vercel
   - Check that variable name starts with `VITE_`

3. **Routing Issues:**
   - `vercel.json` handles SPA routing
   - All routes redirect to `index.html`

4. **API Connection:**
   - Verify backend is running on Render
   - Check CORS settings allow your Vercel domain
   - Test API endpoints directly

## Performance Optimization

### Image Optimization
- Images are cached for 1 year (`vercel.json`)
- Consider using WebP format for better compression
- Optimize image sizes before uploading

### Build Optimization
- Vite automatically optimizes bundle size
- Tree shaking removes unused code
- Assets are fingerprinted for caching

## Monitoring

### Vercel Analytics
- Enable in Project Settings → Analytics
- Monitor page views and performance

### Error Tracking
- Check Vercel Functions logs for errors
- Monitor API response times

## Updates

### Automatic Deployment
- Push to `main` branch triggers automatic deployment
- Preview deployments for pull requests

### Manual Deployment
- Use Vercel CLI: `vercel --prod`
- Or redeploy from Vercel dashboard

---

**Your portfolio is now production-ready! 🚀**
