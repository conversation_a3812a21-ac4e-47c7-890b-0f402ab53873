import { useEffect, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const About = () => {
  const aboutRef = useRef(null);
  const skillsRef = useRef(null);
  const isInView = useInView(aboutRef, { once: true, threshold: 0.3 });

  const skills = [
    { name: 'Photo Editing', level: 95, icon: '📸' },
    { name: 'Photo Manipulation', level: 92, icon: '🎨' },
    { name: 'Portrait Retouching', level: 90, icon: '👤' },
    { name: 'Color Grading', level: 88, icon: '🌈' },
    { name: 'Digital Art', level: 85, icon: '✨' },
    { name: 'Poster Design', level: 87, icon: '🎯' }
  ];

  const tools = [
    'Adobe Premiere Pro',
    'Adobe After Effects',
    'Adobe Illustrator',
    'Adobe Photoshop',
    'Adobe Lightroom'
  ];

  useEffect(() => {
    if (isInView) {
      // Animate skill bars
      skills.forEach((skill, index) => {
        gsap.to(`.skill-bar-${index}`, {
          width: `${skill.level}%`,
          duration: 1.5,
          delay: index * 0.2,
          ease: "power2.out"
        });
      });
    }
  }, [isInView, skills]);

  return (
    <section 
      id="about"
      ref={aboutRef}
      className="min-h-screen bg-black py-20 px-4"
    >
      <div className="max-w-6xl mx-auto">
        {/* Section Title */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-5xl font-bold font-montserrat mb-4">
            <span className="bg-gradient-to-r from-soft-cyan to-warm-coral bg-clip-text text-transparent">
              About Me
            </span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-soft-cyan to-warm-coral mx-auto"></div>
        </motion.div>

        <div className="grid lg:grid-cols-5 gap-12 items-start mb-16">
          {/* Profile Image Section */}
          <motion.div
            className="lg:col-span-2 flex justify-center"
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <div className="relative group max-w-sm">
              {/* Enhanced background decorative elements */}
              <div className="absolute -inset-12 bg-gradient-to-br from-soft-cyan/15 via-warm-coral/10 to-transparent rounded-3xl blur-2xl group-hover:blur-xl transition-all duration-700"></div>
              <div className="absolute -inset-8 bg-gradient-to-tl from-warm-coral/10 via-soft-cyan/15 to-transparent rounded-3xl blur-xl opacity-60"></div>

              {/* Main image container with enhanced styling */}
              <div className="relative">
                {/* Multiple decorative frames for depth */}
                <div className="absolute -inset-8 bg-gradient-to-br from-soft-cyan/25 to-warm-coral/25 rounded-3xl rotate-2 group-hover:rotate-3 transition-transform duration-700 opacity-40"></div>
                <div className="absolute -inset-6 bg-gradient-to-tl from-warm-coral/30 to-soft-cyan/30 rounded-2xl -rotate-1 group-hover:-rotate-2 transition-transform duration-700 opacity-60"></div>
                <div className="absolute -inset-4 bg-gradient-to-br from-soft-cyan/20 to-warm-coral/20 rounded-2xl rotate-1 group-hover:rotate-2 transition-transform duration-500"></div>

                {/* Enhanced profile image container */}
                <div className="relative bg-gradient-to-br from-gray-900 to-gray-800 p-6 rounded-3xl border border-gray-700/50 backdrop-blur-sm shadow-2xl group-hover:shadow-soft-cyan/20 transition-all duration-500">
                  <div className="relative overflow-hidden rounded-2xl">
                    <img
                      src="/images/profile/anshika-profile.jpg"
                      alt="Anshika Singh - Professional Photographer & Digital Editor"
                      className="w-64 h-80 md:w-72 md:h-96 object-cover shadow-2xl group-hover:scale-105 transition-transform duration-700"
                    />

                    {/* Enhanced overlay gradient */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
                    <div className="absolute inset-0 bg-gradient-to-br from-soft-cyan/5 to-warm-coral/5 opacity-60"></div>

                    {/* Professional badge with enhanced styling */}
                    <div className="absolute top-4 left-4 bg-black/90 backdrop-blur-md px-4 py-2 rounded-full border border-soft-cyan/40 shadow-lg">
                      <span className="text-soft-cyan text-sm font-semibold tracking-wide">Professional Photographer</span>
                    </div>

                    {/* Additional credential badge */}
                    <div className="absolute bottom-4 left-4 bg-gradient-to-r from-warm-coral/90 to-soft-cyan/90 backdrop-blur-md px-3 py-1 rounded-full shadow-lg">
                      <span className="text-black text-xs font-bold">Digital Editor</span>
                    </div>
                  </div>

                  {/* Enhanced floating elements */}
                  <div className="absolute -top-3 -right-3 bg-gradient-to-br from-soft-cyan to-warm-coral p-4 rounded-full shadow-xl group-hover:scale-110 transition-transform duration-300">
                    <span className="text-black font-bold text-lg">📸</span>
                  </div>

                  {/* Additional decorative elements */}
                  <div className="absolute -bottom-2 -left-2 bg-gradient-to-br from-warm-coral/80 to-soft-cyan/80 p-2 rounded-full shadow-lg">
                    <span className="text-black font-bold text-sm">✨</span>
                  </div>
                </div>

                {/* Professional certification indicator */}
                <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-gray-900/95 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-700/50 shadow-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-gray-300 text-xs font-medium">Available for Projects</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Bio Section */}
          <motion.div
            className="lg:col-span-3 space-y-6"
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.5 }}
          >


            {/* Professional Bio */}
            <div className="bg-gray-900/30 backdrop-blur-sm p-5 rounded-2xl border border-gray-800/50">
              <h4 className="text-lg font-semibold text-warm-coral mb-3 flex items-center gap-2">
                <span>💼</span>
                Professional Overview
              </h4>
              <div className="space-y-3 text-gray-300 leading-relaxed">
                <p>
                  <span className="text-soft-cyan font-semibold">Passionate visual storyteller</span> specializing in
                  <span className="text-warm-coral font-medium"> photography and advanced digital editing</span>.
                  I transform creative visions into stunning visual narratives that captivate audiences.
                </p>

                <p>
                  My expertise spans <span className="text-soft-cyan font-medium">photo manipulation</span>,
                  <span className="text-warm-coral font-medium"> portrait retouching</span>, and
                  <span className="text-soft-cyan font-medium"> commercial photography</span>.
                  I bring imagination to life through meticulous attention to detail and innovative editing techniques.
                </p>
              </div>
            </div>



            {/* Tools & Software */}
            <div className="bg-gray-900/30 backdrop-blur-sm p-5 rounded-2xl border border-gray-800/50">
              <h4 className="text-lg font-semibold mb-4 text-warm-coral flex items-center gap-2">
                <span>🛠️</span>
                Professional Tools & Software
              </h4>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                {tools.map((tool, index) => (
                  <motion.div
                    key={tool}
                    className="group bg-gray-800/50 hover:bg-gray-700/50 p-3 rounded-lg border border-gray-700/50 hover:border-soft-cyan/30 transition-all duration-300 cursor-pointer"
                    initial={{ opacity: 0, y: 20 }}
                    animate={isInView ? { opacity: 1, y: 0 } : {}}
                    transition={{ delay: 0.8 + index * 0.1 }}
                    whileHover={{ scale: 1.02, y: -2 }}
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-soft-cyan/20 to-warm-coral/20 rounded-lg flex items-center justify-center border border-soft-cyan/30 group-hover:border-soft-cyan/50 transition-colors">
                        <span className="text-sm">
                          {tool.includes('Photoshop') ? '🎨' :
                           tool.includes('Lightroom') ? '📷' :
                           tool.includes('Illustrator') ? '✏️' :
                           tool.includes('Premiere') ? '🎬' :
                           tool.includes('After Effects') ? '✨' : '🔧'}
                        </span>
                      </div>
                      <div className="flex-1">
                        <div className="text-white font-medium group-hover:text-soft-cyan transition-colors text-sm">
                          {tool}
                        </div>
                        <div className="text-xs text-gray-400">
                          {tool.includes('Photoshop') ? 'Image Editing & Manipulation' :
                           tool.includes('Lightroom') ? 'Photo Enhancement & RAW Processing' :
                           tool.includes('Illustrator') ? 'Vector Graphics & Design' :
                           tool.includes('Premiere') ? 'Video Editing & Production' :
                           tool.includes('After Effects') ? 'Motion Graphics & VFX' : 'Professional Tool'}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Proficiency indicator */}
              <div className="mt-3 p-2 bg-gradient-to-r from-soft-cyan/10 to-warm-coral/10 rounded-lg border border-gray-700/30">
                <div className="flex items-center justify-center gap-2 text-xs text-gray-300">
                  <span className="text-green-400">●</span>
                  <span>Expert Level Proficiency in Adobe Creative Suite</span>
                  <span className="text-green-400">●</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Skills Section - Separate centered section */}
        <motion.div
          ref={skillsRef}
          className="max-w-6xl mx-auto"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.7 }}
        >
          <div className="text-center mb-12">
            <h3 className="text-3xl font-semibold font-poppins mb-4 text-warm-coral">
              Photography & Editing Skills
            </h3>
            <div className="w-24 h-1 bg-gradient-to-r from-warm-coral to-soft-cyan mx-auto"></div>
          </div>

          {/* Skills Grid */}
          <div className="grid md:grid-cols-2 gap-8 mb-16">
            {skills.map((skill, index) => (
              <motion.div
                key={skill.name}
                className="bg-gray-900/50 backdrop-blur-sm p-6 rounded-xl border border-gray-800/50 hover:border-soft-cyan/30 transition-all duration-300"
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ delay: 0.9 + index * 0.1 }}
                whileHover={{ scale: 1.02, y: -5 }}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-soft-cyan/20 to-warm-coral/20 rounded-lg flex items-center justify-center border border-soft-cyan/30">
                      <span className="text-2xl">{skill.icon}</span>
                    </div>
                    <div>
                      <span className="font-semibold text-white text-lg">{skill.name}</span>
                      <div className="text-soft-gray text-sm">Professional Level</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className="text-2xl font-bold text-soft-cyan">{skill.level}%</span>
                  </div>
                </div>

                <div className="w-full bg-gray-800 rounded-full h-3 overflow-hidden shadow-inner">
                  <motion.div
                    className="h-full bg-gradient-to-r from-soft-cyan via-warm-coral to-soft-cyan rounded-full shadow-lg"
                    initial={{ width: '0%' }}
                    animate={isInView ? { width: `${skill.level}%` } : { width: '0%' }}
                    transition={{ delay: 1.2 + index * 0.2, duration: 1.5, ease: "easeOut" }}
                  />
                </div>
              </motion.div>
            ))}
          </div>

          {/* Stats Section */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8 p-8 bg-gradient-to-r from-gray-900/80 to-gray-800/80 backdrop-blur-sm rounded-2xl border border-gray-700/50"
            initial={{ opacity: 0, y: 30 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ delay: 1.5 }}
          >
            <motion.div
              className="text-center group"
              whileHover={{ scale: 1.05 }}
            >
              <div className="text-4xl font-bold text-soft-cyan mb-2 group-hover:text-warm-coral transition-colors">10</div>
              <div className="text-soft-gray font-medium">Projects Completed</div>
              <div className="w-12 h-1 bg-gradient-to-r from-soft-cyan to-warm-coral mx-auto mt-2 rounded-full"></div>
            </motion.div>
            <motion.div
              className="text-center group"
              whileHover={{ scale: 1.05 }}
            >
              <div className="text-4xl font-bold text-warm-coral mb-2 group-hover:text-soft-cyan transition-colors">6+</div>
              <div className="text-soft-gray font-medium">Happy Clients</div>
              <div className="w-12 h-1 bg-gradient-to-r from-warm-coral to-soft-cyan mx-auto mt-2 rounded-full"></div>
            </motion.div>
            <motion.div
              className="text-center group"
              whileHover={{ scale: 1.05 }}
            >
              <div className="text-4xl font-bold text-soft-cyan mb-2 group-hover:text-warm-coral transition-colors">1</div>
              <div className="text-soft-gray font-medium">Year Experience</div>
              <div className="w-12 h-1 bg-gradient-to-r from-soft-cyan to-warm-coral mx-auto mt-2 rounded-full"></div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
