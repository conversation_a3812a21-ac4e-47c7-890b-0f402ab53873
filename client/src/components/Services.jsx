import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';

const Services = () => {
  const servicesRef = useRef(null);
  const isInView = useInView(servicesRef, { once: true, threshold: 0.2 });

  const services = [
    {
      id: 1,
      title: "Professional Photography",
      description: "Capture stunning moments and create compelling visual stories through professional photography services.",
      icon: "📸",
      features: [
        "Portrait Photography",
        "Commercial Photography",
        "Product Photography",
        "Creative Photography"
      ]
    },
    {
      id: 2,
      title: "Photo Editing & Retouching",
      description: "Transform your images with professional editing techniques and advanced retouching for flawless results.",
      icon: "🎨",
      features: [
        "Professional Retouching",
        "Color Correction",
        "Background Removal",
        "Skin & Beauty Editing"
      ]
    },
    {
      id: 3,
      title: "Photo Manipulation",
      description: "Create extraordinary visual compositions through advanced photo manipulation and digital artistry.",
      icon: "✨",
      features: [
        "Fantasy Portraits",
        "Composite Images",
        "Creative Manipulation",
        "Digital Art Creation"
      ]
    },
    {
      id: 4,
      title: "Adobe Creative Suite",
      description: "Expert-level proficiency in Adobe Creative Suite for comprehensive photo editing and design solutions.",
      icon: "🛠️",
      features: [
        "Adobe Photoshop",
        "Adobe Lightroom",
        "Adobe Illustrator",
        "Adobe Premiere Pro"
      ]
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const cardVariants = {
    hidden: { 
      opacity: 0, 
      y: 50,
      scale: 0.9
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section 
      id="services"
      ref={servicesRef}
      className="min-h-screen bg-black py-20 px-4"
    >
      <div className="max-w-7xl mx-auto">
        {/* Section Title */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-5xl font-bold font-montserrat mb-4">
            <span className="bg-gradient-to-r from-soft-cyan to-warm-coral bg-clip-text text-transparent">
              My Services
            </span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-soft-cyan to-warm-coral mx-auto mb-8"></div>
          <p className="text-soft-gray max-w-2xl mx-auto">
            I offer a comprehensive range of photography and digital editing services
            to capture and enhance your visual stories with professional expertise.
          </p>
        </motion.div>

        {/* Services Grid */}
        <motion.div
          className="grid md:grid-cols-2 lg:grid-cols-2 gap-8"
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {services.map((service, index) => (
            <motion.div
              key={service.id}
              variants={cardVariants}
              className="group relative bg-deep-charcoal rounded-xl p-8 border border-medium-gray hover:border-soft-cyan transition-all duration-300 cursor-hover overflow-hidden"
              whileHover={{
                scale: 1.02,
                boxShadow: "0 20px 40px rgba(103, 232, 249, 0.1)"
              }}
            >
              {/* Background Gradient on Hover */}
              <div className="absolute inset-0 bg-gradient-to-br from-soft-cyan/5 to-warm-coral/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

              {/* Content */}
              <div className="relative z-10">
                {/* Icon */}
                <motion.div
                  className="text-6xl mb-6"
                  whileHover={{
                    scale: 1.2,
                    rotate: 10
                  }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  {service.icon}
                </motion.div>

                {/* Title */}
                <h3 className="text-2xl font-bold font-montserrat mb-4 text-white group-hover:text-soft-cyan transition-colors">
                  {service.title}
                </h3>

                {/* Description */}
                <p className="text-soft-gray mb-6 leading-relaxed">
                  {service.description}
                </p>

                {/* Features */}
                <ul className="space-y-3 mb-8">
                  {service.features.map((feature, featureIndex) => (
                    <motion.li
                      key={feature}
                      className="flex items-center text-soft-gray"
                      initial={{ opacity: 0, x: -20 }}
                      animate={isInView ? { opacity: 1, x: 0 } : {}}
                      transition={{
                        delay: 0.5 + index * 0.2 + featureIndex * 0.1
                      }}
                    >
                      <div className="w-2 h-2 bg-soft-cyan rounded-full mr-3 flex-shrink-0" />
                      {feature}
                    </motion.li>
                  ))}
                </ul>

                {/* CTA Button */}
                <div className="flex justify-center mt-4">
                  <motion.button
                    className="px-8 py-3 bg-gradient-to-r from-soft-cyan to-warm-coral text-black font-semibold rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 shadow-lg"
                    whileHover={{ scale: 1.05, boxShadow: "0 10px 25px rgba(103, 232, 249, 0.3)" }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => {
                      document.getElementById('contact')?.scrollIntoView({
                        behavior: 'smooth'
                      });
                    }}
                  >
                    Learn More
                  </motion.button>
                </div>
              </div>

              {/* Decorative Elements */}
              <div className="absolute top-4 right-4 w-20 h-20 border border-soft-cyan/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              <div className="absolute bottom-4 left-4 w-12 h-12 border border-warm-coral/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </motion.div>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1 }}
        >
          <p className="text-soft-gray mb-6">
            Have a photography or editing project in mind? Let's bring your vision to life!
          </p>
          <motion.button
            className="px-8 py-4 border-2 border-soft-cyan text-soft-cyan font-semibold rounded-lg cursor-hover"
            whileHover={{
              backgroundColor: '#67e8f9',
              color: '#000',
              scale: 1.05
            }}
            whileTap={{ scale: 0.95 }}
            onClick={() => {
              document.getElementById('contact')?.scrollIntoView({
                behavior: 'smooth'
              });
            }}
          >
            Start Your Project
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
};

export default Services;
