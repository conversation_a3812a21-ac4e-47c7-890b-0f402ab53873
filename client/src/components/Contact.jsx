import { useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import axios from 'axios';
import Footer from './Footer';

// FloatingLabelInput component moved outside to prevent recreation on re-renders
const FloatingLabelInput = ({ label, type = 'text', name, value, onChange, required = false, multiline = false }) => {
  const [isFocused, setIsFocused] = useState(false);
  const InputComponent = multiline ? 'textarea' : 'input';

  const isFloating = isFocused || value.length > 0;

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  return (
    <div className="relative">
      <InputComponent
        type={type}
        name={name}
        value={value}
        onChange={onChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        required={required}
        rows={multiline ? 5 : undefined}
        className="w-full px-4 py-4 bg-deep-charcoal border border-medium-gray rounded-lg text-white placeholder-transparent focus:outline-none focus:border-soft-cyan transition-colors"
        placeholder=" "
      />
      <label
        className={`absolute left-4 transition-all duration-200 pointer-events-none ${
          isFloating
            ? 'text-xs text-soft-cyan -top-2 bg-black px-2'
            : 'text-soft-gray top-4'
        }`}
      >
        {label}
      </label>
    </div>
  );
};

const Contact = () => {
  const contactRef = useRef(null);
  const isInView = useInView(contactRef, { once: true, threshold: 0.2 });
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      const response = await axios.post('http://localhost:5000/api/contact', formData);
      setSubmitStatus({ type: 'success', message: 'Message sent successfully!' });
      setFormData({ name: '', email: '', subject: '', message: '' });
    } catch (error) {
      setSubmitStatus({ 
        type: 'error', 
        message: error.response?.data?.message || 'Failed to send message. Please try again.' 
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const socialLinks = [
    {
      name: 'Instagram',
      icon: '📷',
      url: 'https://www.instagram.com/_motion.muse_/',
      color: 'from-pink-500 to-purple-500'
    },
    {
      name: 'LinkedIn',
      icon: '💼',
      url: 'https://www.linkedin.com/in/anshika-singh-b78190312?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app',
      color: 'from-blue-600 to-blue-400'
    },
    {
      name: 'Email',
      icon: '✉️',
      url: 'mailto:<EMAIL>',
      color: 'from-green-500 to-blue-500'
    }
  ];



  return (
    <section 
      id="contact"
      ref={contactRef}
      className="min-h-screen bg-black py-20 px-4"
    >
      <div className="max-w-6xl mx-auto">
        {/* Section Title */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-5xl font-bold font-montserrat mb-4">
            <span className="bg-gradient-to-r from-soft-cyan to-warm-coral bg-clip-text text-transparent">
              Let's Create Together
            </span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-soft-cyan to-warm-coral mx-auto mb-8"></div>
          <p className="text-soft-gray max-w-2xl mx-auto">
            Ready to bring your stories to life through motion? Get in touch and let's discuss your next animation project.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16">
          {/* Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <FloatingLabelInput
                  label="Your Name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
                <FloatingLabelInput
                  label="Email Address"
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <FloatingLabelInput
                label="Subject"
                name="subject"
                value={formData.subject}
                onChange={handleInputChange}
                required
              />
              
              <FloatingLabelInput
                label="Your Message"
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                multiline
                required
              />

              {/* Submit Status */}
              {submitStatus && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`p-4 rounded-lg ${
                    submitStatus.type === 'success' 
                      ? 'bg-green-900 text-green-300 border border-green-700' 
                      : 'bg-red-900 text-red-300 border border-red-700'
                  }`}
                >
                  {submitStatus.message}
                </motion.div>
              )}

              {/* Submit Button */}
              <motion.button
                type="submit"
                disabled={isSubmitting}
                className="w-full py-4 bg-gradient-to-r from-soft-cyan to-warm-coral text-black font-semibold rounded-lg cursor-hover disabled:opacity-50 disabled:cursor-not-allowed"
                whileHover={{ scale: isSubmitting ? 1 : 1.02 }}
                whileTap={{ scale: isSubmitting ? 1 : 0.98 }}
              >
                {isSubmitting ? 'Sending...' : 'Send Message'}
              </motion.button>
            </form>
          </motion.div>

          {/* Contact Info & Social Links */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="space-y-8"
          >
            {/* Contact Info */}
            <div className="bg-deep-charcoal p-8 rounded-lg border border-medium-gray">
              <h3 className="text-2xl font-semibold mb-6 text-soft-cyan">Get In Touch</h3>

              <div className="space-y-4">
                <div className="flex items-center">
                  <span className="text-2xl mr-4">📧</span>
                  <div>
                    <p className="text-soft-gray text-sm">Email</p>
                    <p className="text-white"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-center">
                  <span className="text-2xl mr-4">📱</span>
                  <div>
                    <p className="text-soft-gray text-sm">Instagram</p>
                    <p className="text-white">@_motion.muse_</p>
                  </div>
                </div>

                <div className="flex items-center">
                  <span className="text-2xl mr-4">📍</span>
                  <div>
                    <p className="text-soft-gray text-sm">Location</p>
                    <p className="text-white">India</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Social Links */}
            <div>
              <h3 className="text-2xl font-semibold mb-6 text-warm-coral">Follow My Work</h3>

              <div className="grid grid-cols-2 gap-4">
                {socialLinks.map((social, index) => (
                  <motion.a
                    key={social.name}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center p-4 bg-deep-charcoal rounded-lg border border-medium-gray hover:border-soft-cyan transition-all cursor-hover group"
                    initial={{ opacity: 0, y: 20 }}
                    animate={isInView ? { opacity: 1, y: 0 } : {}}
                    transition={{ delay: 0.7 + index * 0.1 }}
                    whileHover={{ scale: 1.05 }}
                  >
                    <span className="text-2xl mr-3 group-hover:scale-110 transition-transform">
                      {social.icon}
                    </span>
                    <span className="text-white group-hover:text-soft-cyan transition-colors">
                      {social.name}
                    </span>
                  </motion.a>
                ))}
              </div>
            </div>

            {/* Availability */}
            <motion.div
              className="bg-gradient-to-r from-soft-cyan/10 to-warm-coral/10 p-6 rounded-lg border border-soft-cyan/30"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={isInView ? { opacity: 1, scale: 1 } : {}}
              transition={{ delay: 1 }}
            >
              <div className="flex items-center mb-3">
                <div className="w-3 h-3 bg-warm-coral rounded-full mr-3 animate-pulse"></div>
                <span className="text-warm-coral font-semibold">Available for Projects</span>
              </div>
              <p className="text-soft-gray text-sm">
                Currently accepting new animation projects. Typical response time: 24 hours.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </section>
  );
};

export default Contact;
