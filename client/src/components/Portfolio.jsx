import { useState, useEffect, useRef } from 'react';
import { motion, useInView, AnimatePresence } from 'framer-motion';
import { gsap } from 'gsap';

const Portfolio = () => {
  const portfolioRef = useRef(null);
  const isInView = useInView(portfolioRef, { once: true, threshold: 0.2 });
  const [selectedProject, setSelectedProject] = useState(null);
  const [projects, setProjects] = useState([]);
  const [filter, setFilter] = useState('All');
  const [modalProject, setModalProject] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Categories for filtering
  const categories = ['All', 'Manipulation', 'Matte', 'Poster Design', 'Photo Editing'];

  // Your portfolio projects - replace with your actual images
  const sampleProjects = [
    {
      id: 1,
      title: "Fantasy Portrait Manipulation",
      category: "Manipulation",
      image: "/images/portfolio/manipulation/WhatsApp Image 2025-05-04 at 23.18.21_b96e5f8e.jpg",
      description: "Creative photo manipulation transforming a portrait into a mystical fantasy character with magical elements.",
      tools: ["Adobe Photoshop", "Adobe Lightroom"],
      featured: true
    },
    {
      id: 2,
      title: "Dark Fantasy Castle - Crimson Realm",
      category: "Matte",
      image: "/images/portfolio/matte/WhatsApp Image 2025-05-04 at 23.19.49_6ddcd667.jpg",
      description: "Epic dark fantasy matte painting featuring a gothic castle under a blood moon with dramatic atmospheric lighting and mystical dragon elements.",
      tools: ["Adobe Photoshop", "Digital Painting"],
      featured: true
    },
    {
      id: 4,
      title: "Movie Poster Design",
      category: "Poster Design",
      image: "/images/portfolio/poster-design/WhatsApp Image 2025-05-04 at 23.24.23_ac5b491a.jpg",
      description: "Professional movie poster design with dramatic lighting, typography, and advanced photo compositing techniques.",
      tools: ["Adobe Photoshop", "Adobe Illustrator"],
      featured: true
    },
    {
      id: 8,
      title: "Creative Poster Design",
      category: "Poster Design",
      image: "/images/portfolio/poster-design/WhatsApp Image 2025-05-04 at 23.17.13_ea7c28d1.jpg",
      description: "Innovative poster design showcasing creative typography, visual hierarchy, and compelling graphic elements.",
      tools: ["Adobe Photoshop", "Adobe Illustrator"],
      featured: false
    },
    {
      id: 5,
      title: "Surreal Photo Manipulation",
      category: "Manipulation",
      image: "/images/portfolio/manipulation/WhatsApp Image 2025-05-04 at 23.21.45_fd81f3d9.jpg",
      description: "Complex surreal manipulation blending multiple elements to create an impossible yet believable scene.",
      tools: ["Adobe Photoshop", "Adobe After Effects"],
      featured: false
    },

    {
      id: 6,
      title: "Portrait Photo Retouching",
      category: "Photo Editing",
      image: "/images/portfolio/photo-editing/WhatsApp Image 2025-05-04 at 23.13.59_a8e38b69.jpg",
      description: "Professional portrait retouching with skin enhancement, color grading, and detailed finishing touches.",
      tools: ["Adobe Photoshop", "Adobe Lightroom"],
      featured: true
    },
    {
      id: 9,
      title: "Beauty Photo Enhancement",
      category: "Photo Editing",
      image: "/images/portfolio/photo-editing/WhatsApp Image 2025-05-04 at 23.14.00_7aff76f8.jpg",
      description: "Advanced beauty retouching with natural skin smoothing, eye enhancement, and color correction.",
      tools: ["Adobe Photoshop", "Adobe Lightroom"],
      featured: false
    },
    {
      id: 10,
      title: "Fashion Photo Retouching",
      category: "Photo Editing",
      image: "/images/portfolio/photo-editing/WhatsApp Image 2025-05-04 at 23.14.02_1e3a2072.jpg",
      description: "High-end fashion photo retouching with dramatic lighting adjustments and professional finishing.",
      tools: ["Adobe Photoshop", "Adobe Lightroom"],
      featured: false
    },
    {
      id: 11,
      title: "Creative Photo Enhancement",
      category: "Photo Editing",
      image: "/images/portfolio/photo-editing/WhatsApp Image 2025-05-04 at 23.57.03_75a558f8.jpg",
      description: "Creative photo editing with artistic color grading, mood enhancement, and stylistic adjustments.",
      tools: ["Adobe Photoshop", "Adobe Lightroom"],
      featured: true
    },
    {
      id: 12,
      title: "Professional Portrait Edit",
      category: "Photo Editing",
      image: "/images/portfolio/photo-editing/WhatsApp Image 2025-05-04 at 23.13.59_2ada27d3.jpg",
      description: "Expert portrait editing showcasing natural enhancement and professional color correction techniques.",
      tools: ["Adobe Photoshop", "Adobe Lightroom"],
      featured: false
    },
    {
      id: 13,
      title: "Artistic Photo Retouching",
      category: "Photo Editing",
      image: "/images/portfolio/photo-editing/WhatsApp Image 2025-05-04 at 23.13.59_be971dfa.jpg",
      description: "Creative photo retouching with artistic flair and sophisticated editing techniques.",
      tools: ["Adobe Photoshop", "Adobe Lightroom"],
      featured: false
    },
    {
      id: 14,
      title: "Commercial Photo Edit",
      category: "Photo Editing",
      image: "/images/portfolio/photo-editing/WhatsApp Image 2025-05-04 at 23.14.01_ddcf417a.jpg",
      description: "Commercial-grade photo editing with professional standards and attention to detail.",
      tools: ["Adobe Photoshop", "Adobe Lightroom"],
      featured: false
    },
    {
      id: 15,
      title: "Stylized Photo Enhancement",
      category: "Photo Editing",
      image: "/images/portfolio/photo-editing/WhatsApp Image 2025-05-04 at 23.57.00_e7ce1398.jpg",
      description: "Stylized photo enhancement with creative color grading and mood adjustments.",
      tools: ["Adobe Photoshop", "Adobe Lightroom"],
      featured: true
    }
  ];

  useEffect(() => {
    setProjects(sampleProjects);
  }, []);

  // Filter projects based on selected category
  const filteredProjects = filter === 'All'
    ? projects
    : projects.filter(project => project.category === filter);

  // Modal handlers
  const openModal = (project) => {
    setModalProject(project);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setModalProject(null);
  };

  // Modal Component
  const ProjectModal = ({ project, isOpen, onClose }) => {
    useEffect(() => {
      const handleEscape = (e) => {
        if (e.key === 'Escape') onClose();
      };

      if (isOpen) {
        document.addEventListener('keydown', handleEscape);
        document.body.style.overflow = 'hidden';
      }

      return () => {
        document.removeEventListener('keydown', handleEscape);
        document.body.style.overflow = 'unset';
      };
    }, [isOpen, onClose]);

    if (!isOpen || !project) return null;

    return (
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center p-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        {/* Dark Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-80" />

        {/* Modal Content */}
        <motion.div
          className="relative bg-deep-charcoal rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 w-8 h-8 bg-black bg-opacity-50 hover:bg-opacity-70 rounded-full flex items-center justify-center text-white transition-all"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Image */}
          <div className="w-full">
            <img
              src={project.image || project.afterImage}
              alt={project.title}
              className="w-full h-auto max-h-[60vh] object-contain rounded-t-lg"
            />
          </div>

          {/* Project Details */}
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-2xl font-bold text-white">{project.title}</h3>
              {project.featured && (
                <span className="px-3 py-1 bg-warm-coral text-black text-sm rounded-full">
                  Featured
                </span>
              )}
            </div>

            <p className="text-soft-gray mb-6 leading-relaxed">
              {project.description}
            </p>

            <div className="flex flex-wrap items-center gap-4">
              <div>
                <span className="text-soft-cyan text-sm font-medium block mb-2">Category</span>
                <span className="px-3 py-1 bg-medium-gray text-soft-gray text-sm rounded">
                  {project.category}
                </span>
              </div>

              <div>
                <span className="text-soft-cyan text-sm font-medium block mb-2">Tools Used</span>
                <div className="flex flex-wrap gap-2">
                  {project.tools.map((tool) => (
                    <span
                      key={tool}
                      className="px-3 py-1 bg-medium-gray text-soft-gray text-sm rounded"
                    >
                      {tool}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    );
  };

  // Before/After Slider Component (for manipulation projects)
  const BeforeAfterSlider = ({ beforeImage, afterImage, title, onClick }) => {
    const [sliderPosition, setSliderPosition] = useState(50);
    const [beforeImageError, setBeforeImageError] = useState(false);
    const [afterImageError, setAfterImageError] = useState(false);

    const handleSliderChange = (e) => {
      setSliderPosition(e.target.value);
    };

    // Fallback images for development
    const fallbackBefore = "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=500&h=400&fit=crop";
    const fallbackAfter = "https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=500&h=400&fit=crop";

    return (
      <div className="relative w-full h-64 overflow-hidden rounded-lg cursor-pointer" onClick={onClick}>
        {/* Before Image */}
        <img
          src={beforeImageError ? fallbackBefore : beforeImage}
          alt={`${title} - Before`}
          className="absolute inset-0 w-full h-full object-cover"
          onError={() => setBeforeImageError(true)}
        />

        {/* After Image */}
        <div
          className="absolute inset-0 overflow-hidden"
          style={{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }}
        >
          <img
            src={afterImageError ? fallbackAfter : afterImage}
            alt={`${title} - After`}
            className="w-full h-full object-cover"
            onError={() => setAfterImageError(true)}
          />
        </div>

        {/* Slider */}
        <div className="absolute inset-0 flex items-center">
          <input
            type="range"
            min="0"
            max="100"
            value={sliderPosition}
            onChange={handleSliderChange}
            className="w-full h-full opacity-0 cursor-pointer"
            onClick={(e) => e.stopPropagation()}
          />

          {/* Slider Line */}
          <div
            className="absolute top-0 bottom-0 w-1 bg-soft-cyan pointer-events-none"
            style={{ left: `${sliderPosition}%` }}
          >
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-soft-cyan rounded-full flex items-center justify-center">
              <div className="w-4 h-4 bg-white rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Labels */}
        <div className="absolute top-4 left-4 bg-black bg-opacity-70 px-2 py-1 rounded text-xs text-white">
          BEFORE
        </div>
        <div className="absolute top-4 right-4 bg-black bg-opacity-70 px-2 py-1 rounded text-xs text-white">
          AFTER
        </div>

        {/* Click to view overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all flex items-center justify-center">
          <div className="opacity-0 hover:opacity-100 transition-opacity bg-soft-cyan text-black px-4 py-2 rounded-full text-sm font-medium">
            Click to view details
          </div>
        </div>
      </div>
    );
  };

  // Static Image Component (for matte paintings and single images)
  const StaticImage = ({ image, title, onClick }) => {
    const [imageError, setImageError] = useState(false);
    const fallbackImage = "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=500&h=400&fit=crop";

    return (
      <div className="relative w-full h-64 overflow-hidden rounded-lg cursor-pointer group" onClick={onClick}>
        <img
          src={imageError ? fallbackImage : image}
          alt={title}
          className="w-full h-full object-cover transition-transform group-hover:scale-105"
          onError={() => setImageError(true)}
        />

        {/* Hover overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity bg-soft-cyan text-black px-4 py-2 rounded-full text-sm font-medium">
            Click to view details
          </div>
        </div>
      </div>
    );
  };

  return (
    <section 
      id="portfolio"
      ref={portfolioRef}
      className="min-h-screen bg-black py-20 px-4"
    >
      <div className="max-w-7xl mx-auto">
        {/* Section Title */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-5xl font-bold font-montserrat mb-4">
            <span className="bg-gradient-to-r from-soft-cyan to-warm-coral bg-clip-text text-transparent">
              My Portfolio
            </span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-soft-cyan to-warm-coral mx-auto mb-8"></div>
          <p className="text-soft-gray max-w-2xl mx-auto">
            Explore my collection of photo manipulations, matte paintings, and design work.
            Each project showcases different creative techniques and artistic approaches to visual storytelling.
          </p>
        </motion.div>

        {/* Filter Buttons */}
        <motion.div
          className="flex flex-wrap justify-center gap-4 mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.3 }}
        >
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setFilter(category)}
              className={`px-6 py-3 rounded-full font-medium transition-all cursor-hover ${
                filter === category
                  ? 'bg-soft-cyan text-black'
                  : 'border border-soft-cyan text-soft-cyan hover:bg-soft-cyan hover:text-black'
              }`}
            >
              {category}
            </button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <motion.div
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          layout
        >
          <AnimatePresence>
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                layout
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-deep-charcoal rounded-lg overflow-hidden border border-medium-gray hover:border-soft-cyan transition-all"
              >
                {/* Render appropriate image component based on category */}
                <StaticImage
                  image={project.image || project.afterImage}
                  title={project.title}
                  onClick={() => openModal(project)}
                />

                <div className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-xl font-semibold text-white">{project.title}</h3>
                    {project.featured && (
                      <span className="px-2 py-1 bg-warm-coral text-black text-xs rounded-full">
                        Featured
                      </span>
                    )}
                  </div>

                  <p className="text-soft-gray text-sm mb-4 line-clamp-2">
                    {project.description}
                  </p>

                  <div className="flex items-center justify-between">
                    <span className="text-soft-cyan text-sm font-medium">
                      {project.category}
                    </span>

                    <div className="flex gap-2">
                      {project.tools.map((tool) => (
                        <span
                          key={tool}
                          className="px-2 py-1 bg-medium-gray text-soft-gray text-xs rounded"
                        >
                          {tool}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {/* Modal */}
        <AnimatePresence>
          <ProjectModal
            project={modalProject}
            isOpen={isModalOpen}
            onClose={closeModal}
          />
        </AnimatePresence>

      </div>
    </section>
  );
};

export default Portfolio;
