import { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';

const Hero = () => {
  const heroRef = useRef(null);
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const fullText = "Cinematic Eyes. Editorial Precision";

  useEffect(() => {
    // Typewriter effect
    if (currentIndex < fullText.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + fullText[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, 100);
      return () => clearTimeout(timeout);
    }
  }, [currentIndex, fullText]);

  useEffect(() => {
    // GSAP animations for background elements
    const tl = gsap.timeline({ repeat: -1 });
    
    // Floating particles animation
    gsap.set('.particle', { 
      x: () => Math.random() * window.innerWidth,
      y: () => Math.random() * window.innerHeight,
      opacity: 0.3
    });

    gsap.to('.particle', {
      y: '-=100',
      duration: 10,
      repeat: -1,
      ease: 'none',
      stagger: 0.5
    });

    return () => tl.kill();
  }, []);

  const scrollToPortfolio = () => {
    document.getElementById('portfolio')?.scrollIntoView({ 
      behavior: 'smooth' 
    });
  };

  const scrollToContact = () => {
    document.getElementById('contact')?.scrollIntoView({ 
      behavior: 'smooth' 
    });
  };

  return (
    <section
      id="hero"
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center bg-black overflow-hidden"
    >
      {/* Animated background particles */}
      {[...Array(20)].map((_, i) => (
        <div
          key={i}
          className="particle absolute w-2 h-2 bg-soft-cyan rounded-full opacity-20"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animationDelay: `${Math.random() * 5}s`
          }}
        />
      ))}

      {/* Main content */}
      <div className="text-center z-10 px-4">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.5 }}
        >
          {/* Profile Image */}
          <motion.div
            className="flex justify-center mb-12"
            initial={{ opacity: 0, scale: 0.8, y: -20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.2 }}
          >
            <div className="relative group">
              {/* Outer glow ring */}
              <div className="absolute -inset-4 bg-gradient-to-r from-soft-cyan via-warm-coral to-soft-cyan rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-500 blur-lg"></div>

              {/* Main image container */}
              <div className="relative">
                <img
                  src="/images/profile/anshika-profile.jpg"
                  alt="Professional Photographer & Digital Editor"
                  className="w-40 h-40 rounded-full object-cover border-4 border-white/20 shadow-2xl backdrop-blur-sm"
                />

                {/* Animated border */}
                <div className="absolute inset-0 rounded-full border-2 border-soft-cyan/50 animate-pulse"></div>

                {/* Subtle overlay */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-transparent via-soft-cyan/5 to-warm-coral/10"></div>
              </div>

              {/* Floating accent dots */}
              <div className="absolute -top-2 -right-2 w-4 h-4 bg-warm-coral rounded-full animate-bounce"></div>
              <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-soft-cyan rounded-full animate-pulse"></div>
            </div>
          </motion.div>
          {/* Name/Brand */}
          <motion.h1
            className="text-6xl md:text-8xl font-bold font-montserrat mb-6 glitch"
            data-text="ANSHIKA SINGH"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <span className="bg-gradient-to-r from-soft-cyan via-warm-coral to-soft-cyan bg-clip-text text-transparent">
              ANSHIKA SINGH
            </span>
          </motion.h1>

          {/* Typewriter tagline */}
          <div className="h-16 mb-8">
            <p className="text-xl md:text-2xl font-poppins text-soft-gray">
              {displayText}
              <motion.span
                className="inline-block w-1 h-6 bg-soft-cyan ml-1"
                animate={{ opacity: [1, 0] }}
                transition={{ duration: 0.8, repeat: Infinity }}
              />
            </p>
          </div>

          {/* Specializations */}
          <motion.div 
            className="flex flex-wrap justify-center gap-4 mb-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2, duration: 1 }}
          >
            {['Photography', 'Photo Editing', 'Digital Art'].map((skill, index) => (
              <motion.span
                key={skill}
                className="px-4 py-2 border border-soft-cyan text-soft-cyan rounded-full text-sm font-poppins cursor-hover"
                whileHover={{
                  backgroundColor: '#67e8f9',
                  color: '#000',
                  scale: 1.05
                }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 2.5 + index * 0.2 }}
              >
                {skill}
              </motion.span>
            ))}
          </motion.div>

          {/* CTA Buttons */}
          <motion.div 
            className="flex flex-col sm:flex-row gap-6 justify-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 3, duration: 0.8 }}
          >
            <motion.button
              onClick={scrollToPortfolio}
              className="px-8 py-4 bg-gradient-to-r from-soft-cyan to-warm-coral text-black font-semibold rounded-lg cursor-hover"
              whileHover={{ scale: 1.05, boxShadow: "0 0 30px #67e8f9" }}
              whileTap={{ scale: 0.95 }}
            >
              View My Work
            </motion.button>

            <motion.button
              onClick={scrollToContact}
              className="px-8 py-4 border-2 border-soft-cyan text-soft-cyan font-semibold rounded-lg cursor-hover"
              whileHover={{
                backgroundColor: '#67e8f9',
                color: '#000',
                scale: 1.05
              }}
              whileTap={{ scale: 0.95 }}
            >
              Contact Me
            </motion.button>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll indicator */}
      <motion.div
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        animate={{ y: [0, 10, 0] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <div className="w-6 h-10 border-2 border-soft-cyan rounded-full flex justify-center">
          <motion.div
            className="w-1 h-3 bg-soft-cyan rounded-full mt-2"
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </div>
      </motion.div>
    </section>
  );
};

export default Hero;
