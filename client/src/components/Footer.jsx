import { motion } from 'framer-motion';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-black border-t border-gray-800/50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          {/* Made with love */}
          <motion.div
            className="flex items-center gap-2 text-soft-gray"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <span className="text-sm">Made with</span>
            <motion.span
              className="text-red-500 text-lg"
              animate={{ 
                scale: [1, 1.2, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            >
              ❤️
            </motion.span>
            <span className="text-sm">by</span>
            <motion.span
              className="text-soft-cyan font-semibold hover:text-warm-coral transition-colors cursor-pointer"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              CodeAfe
            </motion.span>
          </motion.div>

          {/* Copyright */}
          <motion.div
            className="text-soft-gray text-sm text-center md:text-right"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <p>© {currentYear} All rights reserved to CodeAfe</p>
          </motion.div>
        </div>

        {/* Decorative line */}
        <motion.div
          className="mt-6 h-px bg-gradient-to-r from-transparent via-soft-cyan/30 to-transparent"
          initial={{ scaleX: 0 }}
          animate={{ scaleX: 1 }}
          transition={{ duration: 1, delay: 0.4 }}
        />

        {/* Additional branding */}
        <motion.div
          className="mt-4 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <p className="text-xs text-gray-500">
            Crafted with passion for exceptional digital experiences
          </p>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
