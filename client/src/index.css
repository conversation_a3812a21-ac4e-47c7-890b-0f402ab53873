@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px;
}

body {
  font-family: 'Poppins', sans-serif;
  background-color: #000000;
  color: #ffffff;
  overflow-x: hidden;
  line-height: 1.6;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #000000;
}

::-webkit-scrollbar-thumb {
  background: #67e8f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #fb7185;
}

/* Custom cursor */
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: #67e8f9;
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: transform 0.1s ease;
}

.custom-cursor.hover {
  transform: scale(2);
  background: #fb7185;
}

/* Glitch effect */
.glitch {
  position: relative;
}

.glitch::before,
.glitch::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch::before {
  animation: glitch-1 0.5s infinite;
  color: #fb7185;
  z-index: -1;
}

.glitch::after {
  animation: glitch-2 0.5s infinite;
  color: #67e8f9;
  z-index: -2;
}

@keyframes glitch-1 {
  0%, 14%, 15%, 49%, 50%, 99%, 100% {
    transform: translate(0);
  }
  15%, 49% {
    transform: translate(-2px, -2px);
  }
}

@keyframes glitch-2 {
  0%, 20%, 21%, 62%, 63%, 99%, 100% {
    transform: translate(0);
  }
  21%, 62% {
    transform: translate(2px, 2px);
  }
}

/* Enhanced profile image effects */
.profile-glow {
  filter: drop-shadow(0 0 20px rgba(103, 232, 249, 0.3));
}

.profile-frame {
  background: linear-gradient(135deg, rgba(103, 232, 249, 0.1), rgba(251, 113, 133, 0.1));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Floating animation for accent elements */
@keyframes float-gentle {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(5deg); }
}

.float-gentle {
  animation: float-gentle 4s ease-in-out infinite;
}

/* Enhanced smooth scrolling and UX */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid #67e8f9;
  outline-offset: 2px;
}

/* Enhanced button hover effects */
.btn-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(103, 232, 249, 0.2);
}

/* Parallax container */
.parallax-container {
  transform-style: preserve-3d;
  perspective: 1000px;
}

/* Scroll reveal animations */
.scroll-reveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Stagger animation delays */
.scroll-reveal:nth-child(1) { transition-delay: 0.1s; }
.scroll-reveal:nth-child(2) { transition-delay: 0.2s; }
.scroll-reveal:nth-child(3) { transition-delay: 0.3s; }
.scroll-reveal:nth-child(4) { transition-delay: 0.4s; }

/* Enhanced loading states */
.loading-shimmer {
  background: linear-gradient(90deg, transparent, rgba(103, 232, 249, 0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Improved text selection */
::selection {
  background: rgba(103, 232, 249, 0.3);
  color: #ffffff;
}

::-moz-selection {
  background: rgba(103, 232, 249, 0.3);
  color: #ffffff;
}

/* Enhanced scroll indicators */
.scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, #67e8f9, #fb7185);
  transform-origin: left;
  z-index: 1000;
}

/* Smooth transitions for all interactive elements */
a, button, input, textarea, select {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced backdrop blur support */
@supports (backdrop-filter: blur(10px)) {
  .backdrop-blur-enhanced {
    backdrop-filter: blur(10px) saturate(180%);
  }
}

/* Improved mobile touch targets */
@media (max-width: 768px) {
  button, a {
    min-height: 44px;
    min-width: 44px;
  }
}
